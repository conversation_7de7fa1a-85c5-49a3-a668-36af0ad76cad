import { <PERSON>, <PERSON><PERSON>, Card, CardBody } from '@heroui/react';
import Image from 'next/image';

interface Recommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  image: string;
  badge: string;
}

interface RecommendationCardProps {
  recommendations: Recommendation[];
  showViewAll?: boolean;
}

const RecommendationCard = ({
  recommendations,
  showViewAll = true
}: RecommendationCardProps) => {
  return (
    <div className="flex flex-col h-full">
      <div className="flex flex-col gap-0 flex-1">
        {recommendations.map((item: Recommendation, index: number) => (
          <Card
            key={`${item.title}-${index}`}
            className="bg-white border-none shadow-none hover:shadow hover:border-2 hover:border-black transition-all duration-300 ease-in-out"
            isHoverable
            isPressable
          >
            <CardBody>
              <div className="flex items-center justify-between rounded-xl cursor-pointer w-full">
                <div className="flex gap-4">
                  <div className="relative">
                    <Image
                      src={item.image}
                      alt={item.title}
                      width={120}
                      height={95}
                      className="w-[120px] h-[95px] object-cover rounded-xl"
                    />
                    <Chip
                      className="absolute -bottom-2 right-2 font-medium bg-[#5A5A5A] text-white border-2 border-white text-[10px] rounded-full h-[16px]"
                      color="primary"
                      variant="flat"
                      size="sm"
                    >
                      {item.badge}
                    </Chip>
                  </div>
                  <div className="text-sm space-y-1">
                    <p className="font-medium text-base text-[#1C1C1C]">
                      {item.title}
                    </p>
                    <p className="text-[#808080] text-xs">{item.duration}</p>
                    <p className="text-[#808080]  text-xs">{item.location}</p>
                    <p className="text-xs text-[#808080] ">{item.tags}</p>
                  </div>
                </div>
                <div>
                  <Chip
                    color="primary"
                    variant="flat"
                    size="md"
                    className="font-semibold h-[24px]"
                  >
                    Details
                  </Chip>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {showViewAll && (
        <div className="flex justify-end mt-2">
          <Button
            variant="light"
            size="sm"
            color="primary"
            className="rounded-full font-semibold p-0 h-[24px]"
          >
            View All
          </Button>
        </div>
      )}
    </div>
  );
};

export default RecommendationCard;
